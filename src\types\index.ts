// Global type definitions for UGC Kit Pro

export interface User {
  id: string;
  email: string;
  name: string;
  plan: UserPlan;
  createdAt: Date;
  updatedAt: Date;
}

export interface UserPlan {
  key: 'starter' | 'pro' | 'enterprise';
  name: string;
  price: number;
  features: string[];
  limits: {
    scriptsPerMonth: number;
    templatesAccess: boolean;
    prioritySupport: boolean;
    customBranding: boolean;
  };
}

export interface Script {
  id: string;
  title: string;
  content: string;
  type: ScriptType;
  tags: string[];
  createdAt: Date;
  updatedAt: Date;
  userId: string;
}

export type ScriptType = 
  | 'hook'
  | 'testimonial'
  | 'product-demo'
  | 'unboxing'
  | 'tutorial'
  | 'comparison'
  | 'lifestyle';

export interface Template {
  id: string;
  name: string;
  description: string;
  category: string;
  content: string;
  variables: TemplateVariable[];
  isPremium: boolean;
}

export interface TemplateVariable {
  name: string;
  type: 'text' | 'number' | 'select' | 'textarea';
  label: string;
  placeholder?: string;
  options?: string[];
  required: boolean;
}

export interface GenerationRequest {
  templateId: string;
  variables: Record<string, any>;
  tone?: 'casual' | 'professional' | 'enthusiastic' | 'conversational';
  length?: 'short' | 'medium' | 'long';
}

export interface GenerationResponse {
  id: string;
  content: string;
  metadata: {
    templateUsed: string;
    generatedAt: Date;
    tokensUsed: number;
  };
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Component Props Types
export interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
}

export interface FormFieldProps extends BaseComponentProps {
  label: string;
  error?: string;
  required?: boolean;
}

// Navigation Types
export interface NavItem {
  label: string;
  href: string;
  icon?: string;
  children?: NavItem[];
}

// Dashboard Types
export interface DashboardStats {
  totalScripts: number;
  scriptsThisMonth: number;
  templatesUsed: number;
  averageRating: number;
}

// Settings Types
export interface UserSettings {
  notifications: {
    email: boolean;
    push: boolean;
    marketing: boolean;
  };
  preferences: {
    theme: 'light' | 'dark' | 'system';
    language: string;
    timezone: string;
  };
  privacy: {
    profileVisible: boolean;
    analyticsEnabled: boolean;
  };
}
