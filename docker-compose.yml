version: '3.8'

services:
  # Development service
  app-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
    ports:
      - "8080:8080"
    volumes:
      - .:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
    profiles:
      - dev

  # Production service
  app-prod:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "80:80"
    environment:
      - NODE_ENV=production
    profiles:
      - prod

  # Testing service
  app-test:
    build:
      context: .
      dockerfile: Dockerfile.dev
    command: npm run test
    volumes:
      - .:/app
      - /app/node_modules
    environment:
      - NODE_ENV=test
    profiles:
      - test
