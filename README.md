# 🚀 UGC Kit Pro - Viral Content Generator

> Create viral UGC content in seconds with our AI-powered toolkit. Transform your content strategy and boost engagement effortlessly.

[![CI/CD Pipeline](https://github.com/your-username/viral-funnel-flow/workflows/CI/CD%20Pipeline/badge.svg)](https://github.com/your-username/viral-funnel-flow/actions)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![TypeScript](https://img.shields.io/badge/TypeScript-007ACC?logo=typescript&logoColor=white)](https://typescriptlang.org/)
[![React](https://img.shields.io/badge/React-20232A?logo=react&logoColor=61DAFB)](https://reactjs.org/)

## ✨ Features

- 🎯 **AI-Powered Script Generation** - Create viral UGC scripts in seconds
- 📱 **Responsive Design** - Works perfectly on all devices
- 🎨 **Modern UI** - Built with shadcn/ui and Tailwind CSS
- 🔒 **Type-Safe** - Full TypeScript support
- ⚡ **Fast Performance** - Optimized with Vite
- 🧪 **Testing Ready** - Configured with Vitest
- 🐳 **Docker Support** - Easy deployment with Docker
- 📊 **Analytics Dashboard** - Track your content performance
- 💳 **Payment Integration** - Stripe-ready payment system

## 🛠️ Tech Stack

- **Frontend**: React 18, TypeScript, Vite
- **Styling**: Tailwind CSS, shadcn/ui
- **State Management**: React Query (TanStack Query)
- **Routing**: React Router DOM
- **Forms**: React Hook Form + Zod validation
- **Testing**: Vitest, React Testing Library
- **Build Tool**: Vite
- **Package Manager**: npm
- **Deployment**: Docker, Nginx

## 🚀 Quick Start

### Prerequisites

- Node.js 18.x or higher
- npm or yarn
- Git

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/your-username/viral-funnel-flow.git
   cd viral-funnel-flow
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env.local
   # Edit .env.local with your configuration
   ```

4. **Start development server**
   ```bash
   npm run dev
   ```

5. **Open your browser**
   Navigate to `http://localhost:8080`

## 📁 Project Structure

```
viral-funnel-flow/
├── .github/workflows/     # CI/CD pipelines
├── docs/                  # Documentation
├── public/               # Static assets
├── src/
│   ├── components/       # React components
│   │   ├── ui/          # shadcn/ui components
│   │   ├── dashboard/   # Dashboard components
│   │   └── sections/    # Page sections
│   ├── hooks/           # Custom React hooks
│   ├── lib/             # Utility libraries
│   ├── pages/           # Page components
│   ├── types/           # TypeScript definitions
│   └── utils/           # Helper functions
├── tests/               # Test files
├── Dockerfile           # Production Docker config
├── docker-compose.yml   # Docker Compose config
└── vitest.config.ts     # Test configuration
```

## 🧪 Development

### Available Scripts

```bash
# Development
npm run dev              # Start development server
npm run build           # Build for production
npm run preview         # Preview production build

# Code Quality
npm run lint            # Run ESLint
npm run lint:fix        # Fix ESLint issues
npm run type-check      # Run TypeScript checks
npm run format          # Format code with Prettier

# Testing
npm run test            # Run tests
npm run test:ui         # Run tests with UI
npm run test:coverage   # Run tests with coverage

# Docker
npm run docker:dev      # Run development with Docker
npm run docker:prod     # Run production with Docker
npm run docker:test     # Run tests with Docker
```

### Code Style

This project uses:
- **ESLint** for code linting
- **Prettier** for code formatting
- **TypeScript** for type safety
- **Conventional Commits** for commit messages

## 🐳 Docker Deployment

### Development
```bash
docker-compose --profile dev up
```

### Production
```bash
docker-compose --profile prod up
```

### Testing
```bash
docker-compose --profile test up
```

## 🧪 Testing

Run the test suite:
```bash
npm run test
```

Run tests with coverage:
```bash
npm run test:coverage
```

Run tests with UI:
```bash
npm run test:ui
```

## 📚 Documentation

- [Contributing Guide](docs/CONTRIBUTING.md)
- [API Documentation](docs/API.md)
- [Deployment Guide](docs/DEPLOYMENT.md)

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](docs/CONTRIBUTING.md) for details.

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'feat: add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [shadcn/ui](https://ui.shadcn.com/) for the beautiful UI components
- [Tailwind CSS](https://tailwindcss.com/) for the utility-first CSS framework
- [Vite](https://vitejs.dev/) for the blazing fast build tool
- [React](https://reactjs.org/) for the amazing frontend library

## 📞 Support

- 📧 Email: <EMAIL>
- 💬 Discord: [Join our community](https://discord.gg/ugckitpro)
- 📖 Documentation: [docs.ugckitpro.com](https://docs.ugckitpro.com)

---

Made with ❤️ by the UGC Kit Pro team
