# Environment Configuration Example
# Copy this file to .env.local and fill in your actual values

# Application
VITE_APP_NAME="UGC Kit Pro"
VITE_APP_VERSION="1.0.0"
VITE_APP_ENV="development"

# API Configuration
VITE_API_BASE_URL="https://api.ugckitpro.com"
VITE_API_TIMEOUT=10000

# Authentication
VITE_AUTH_DOMAIN="your-auth-domain.com"
VITE_AUTH_CLIENT_ID="your-client-id"

# Analytics
VITE_GOOGLE_ANALYTICS_ID="GA-XXXXXXXXX"
VITE_HOTJAR_ID="your-hotjar-id"

# Payment Processing
VITE_STRIPE_PUBLISHABLE_KEY="pk_test_xxxxxxxxxx"

# Feature Flags
VITE_ENABLE_BETA_FEATURES="false"
VITE_ENABLE_DEBUG_MODE="false"

# Social Media
VITE_TWITTER_HANDLE="@ugckitpro"
VITE_FACEBOOK_PAGE="ugckitpro"
VITE_INSTAGRAM_HANDLE="ugckitpro"

# Support
VITE_SUPPORT_EMAIL="<EMAIL>"
VITE_CONTACT_EMAIL="<EMAIL>"
