# Contributing to UGC Kit Pro

Thank you for your interest in contributing to UGC Kit Pro! This document provides guidelines and information for contributors.

## 🚀 Getting Started

### Prerequisites
- Node.js 18.x or higher
- npm or yarn package manager
- Git

### Development Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd viral-funnel-flow
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env.local
   # Edit .env.local with your configuration
   ```

4. **Start development server**
   ```bash
   npm run dev
   ```

## 📁 Project Structure

```
src/
├── components/          # React components
│   ├── ui/             # shadcn/ui components
│   ├── dashboard/      # Dashboard-specific components
│   └── sections/       # Page sections
├── pages/              # Page components
├── hooks/              # Custom React hooks
├── lib/                # Utility libraries
├── types/              # TypeScript type definitions
└── utils/              # Helper functions
```

## 🎯 Development Guidelines

### Code Style
- Use TypeScript for all new code
- Follow ESLint configuration
- Use Prettier for code formatting
- Write meaningful commit messages

### Component Guidelines
- Use functional components with hooks
- Implement proper TypeScript types
- Follow the existing naming conventions
- Add JSDoc comments for complex functions

### Testing
- Write unit tests for utilities and hooks
- Add integration tests for complex components
- Ensure all tests pass before submitting PR

## 🔄 Pull Request Process

1. **Create a feature branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Make your changes**
   - Follow coding standards
   - Add tests if applicable
   - Update documentation

3. **Test your changes**
   ```bash
   npm run test
   npm run lint
   npm run build
   ```

4. **Submit pull request**
   - Provide clear description
   - Reference related issues
   - Request review from maintainers

## 🐛 Bug Reports

When reporting bugs, please include:
- Clear description of the issue
- Steps to reproduce
- Expected vs actual behavior
- Browser/environment information
- Screenshots if applicable

## 💡 Feature Requests

For feature requests:
- Check existing issues first
- Provide detailed use case
- Explain the benefit to users
- Consider implementation complexity

## 📝 Documentation

- Update README.md for user-facing changes
- Add JSDoc comments for new functions
- Update type definitions
- Include examples where helpful

## 🏷️ Commit Convention

Use conventional commits:
- `feat:` new features
- `fix:` bug fixes
- `docs:` documentation changes
- `style:` formatting changes
- `refactor:` code refactoring
- `test:` adding tests
- `chore:` maintenance tasks

## 📞 Getting Help

- Check existing documentation
- Search through issues
- Ask questions in discussions
- Contact maintainers directly

Thank you for contributing! 🎉
