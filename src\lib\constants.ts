// Application constants

export const APP_CONFIG = {
  name: 'UGC Kit Pro',
  version: '1.0.0',
  description: 'Create viral UGC content in seconds with our AI-powered toolkit',
  url: 'https://ugckitpro.com',
  supportEmail: '<EMAIL>',
  contactEmail: '<EMAIL>',
} as const;

export const API_CONFIG = {
  baseUrl: import.meta.env.VITE_API_BASE_URL || 'https://api.ugckitpro.com',
  timeout: 10000,
  retryAttempts: 3,
} as const;

export const ROUTES = {
  HOME: '/',
  DASHBOARD: '/dashboard',
  LOGIN: '/login',
  REGISTER: '/register',
  PAYMENT: '/payment',
  ABOUT: '/about',
  BLOG: '/blog',
  CONTACT: '/contact',
  TERMS: '/terms',
  PRIVACY: '/privacy',
  RETURNS: '/returns',
} as const;

export const USER_PLANS = {
  STARTER: {
    key: 'starter',
    name: 'Starter',
    price: 0,
    features: [
      '5 scripts per month',
      'Basic templates',
      'Email support',
      'Standard generation speed'
    ],
    limits: {
      scriptsPerMonth: 5,
      templatesAccess: false,
      prioritySupport: false,
      customBranding: false,
    }
  },
  PRO: {
    key: 'pro',
    name: 'Pro',
    price: 29,
    features: [
      'Unlimited scripts',
      'Premium templates',
      'Priority support',
      'Fast generation',
      'Analytics dashboard'
    ],
    limits: {
      scriptsPerMonth: -1, // unlimited
      templatesAccess: true,
      prioritySupport: true,
      customBranding: false,
    }
  },
  ENTERPRISE: {
    key: 'enterprise',
    name: 'Enterprise',
    price: 99,
    features: [
      'Everything in Pro',
      'Custom branding',
      'API access',
      'Dedicated support',
      'Custom integrations'
    ],
    limits: {
      scriptsPerMonth: -1, // unlimited
      templatesAccess: true,
      prioritySupport: true,
      customBranding: true,
    }
  }
} as const;

export const SCRIPT_TYPES = {
  HOOK: 'hook',
  TESTIMONIAL: 'testimonial',
  PRODUCT_DEMO: 'product-demo',
  UNBOXING: 'unboxing',
  TUTORIAL: 'tutorial',
  COMPARISON: 'comparison',
  LIFESTYLE: 'lifestyle',
} as const;

export const TONE_OPTIONS = [
  { value: 'casual', label: 'Casual & Friendly' },
  { value: 'professional', label: 'Professional' },
  { value: 'enthusiastic', label: 'Enthusiastic' },
  { value: 'conversational', label: 'Conversational' },
] as const;

export const LENGTH_OPTIONS = [
  { value: 'short', label: 'Short (30-60 seconds)' },
  { value: 'medium', label: 'Medium (60-90 seconds)' },
  { value: 'long', label: 'Long (90+ seconds)' },
] as const;

export const SOCIAL_LINKS = {
  TWITTER: 'https://twitter.com/ugckitpro',
  FACEBOOK: 'https://facebook.com/ugckitpro',
  INSTAGRAM: 'https://instagram.com/ugckitpro',
  LINKEDIN: 'https://linkedin.com/company/ugckitpro',
  YOUTUBE: 'https://youtube.com/@ugckitpro',
} as const;

export const VALIDATION_RULES = {
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  PASSWORD_MIN_LENGTH: 8,
  NAME_MIN_LENGTH: 2,
  NAME_MAX_LENGTH: 50,
  SCRIPT_TITLE_MAX_LENGTH: 100,
  SCRIPT_CONTENT_MAX_LENGTH: 5000,
} as const;

export const LOCAL_STORAGE_KEYS = {
  USER_PREFERENCES: 'ugc_user_preferences',
  DRAFT_SCRIPTS: 'ugc_draft_scripts',
  RECENT_TEMPLATES: 'ugc_recent_templates',
  THEME: 'ugc_theme',
} as const;

export const ERROR_MESSAGES = {
  GENERIC: 'Something went wrong. Please try again.',
  NETWORK: 'Network error. Please check your connection.',
  UNAUTHORIZED: 'Please log in to continue.',
  FORBIDDEN: 'You don\'t have permission to perform this action.',
  NOT_FOUND: 'The requested resource was not found.',
  VALIDATION: 'Please check your input and try again.',
  RATE_LIMIT: 'Too many requests. Please wait a moment.',
} as const;

export const SUCCESS_MESSAGES = {
  SCRIPT_GENERATED: 'Script generated successfully!',
  SCRIPT_SAVED: 'Script saved to your library.',
  PROFILE_UPDATED: 'Profile updated successfully.',
  PASSWORD_CHANGED: 'Password changed successfully.',
  EMAIL_SENT: 'Email sent successfully.',
} as const;
